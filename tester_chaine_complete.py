#!/usr/bin/env python3
"""
Test complet de la chaîne : Postfix (Linux) -> hMailServer (Windows) -> Outlook
Script de validation de l'architecture complète
"""

import subprocess
import socket
import smtplib
import time
import requests
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import os

class TesteurChaineComplete:
    """Testeur pour la chaîne complète Postfix -> hMailServer -> Outlook"""
    
    def __init__(self):
        self.hmailserver_ip = None
        self.app_url = 'http://127.0.0.1:5000'
        self.resultats_tests = {}
        
    def detecter_hmailserver(self):
        """Détecte hMailServer rapidement"""
        print("🔍 DÉTECTION HMAILSERVER")
        print("="*30)
        
        ips_candidates = [
            "*************", "************", "********", "**********"
        ]
        
        for ip in ips_candidates:
            if self.tester_port_smtp(ip):
                self.hmailserver_ip = ip
                print(f"✅ hMailServer trouvé : {ip}")
                return True
        
        # Saisie manuelle
        ip_manuelle = input("📧 IP hMailServer : ").strip()
        if ip_manuelle and self.tester_port_smtp(ip_manuelle):
            self.hmailserver_ip = ip_manuelle
            return True
        
        return False
    
    def tester_port_smtp(self, ip, port=25, timeout=2):
        """Test rapide du port SMTP"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def test_1_postfix_local(self):
        """Test 1: Postfix local accessible"""
        print("\n🧪 TEST 1: POSTFIX LOCAL")
        print("-" * 30)
        
        try:
            # Test status
            result = subprocess.run(["systemctl", "is-active", "postfix"], 
                                  capture_output=True, text=True)
            if result.stdout.strip() != "active":
                print("❌ Postfix non actif")
                self.resultats_tests['postfix_status'] = False
                return False
            
            # Test connexion SMTP
            server = smtplib.SMTP('127.0.0.1', 25, timeout=5)
            server.quit()
            
            print("✅ Postfix local accessible")
            self.resultats_tests['postfix_local'] = True
            return True
            
        except Exception as e:
            print(f"❌ Erreur Postfix local : {e}")
            self.resultats_tests['postfix_local'] = False
            return False
    
    def test_2_connectivite_hmailserver(self):
        """Test 2: Connectivité vers hMailServer"""
        print("\n🧪 TEST 2: CONNECTIVITÉ HMAILSERVER")
        print("-" * 40)
        
        if not self.hmailserver_ip:
            print("❌ IP hMailServer non définie")
            self.resultats_tests['hmailserver_connectivite'] = False
            return False
        
        try:
            # Test connexion SMTP complète
            server = smtplib.SMTP(self.hmailserver_ip, 25, timeout=10)
            server.ehlo()
            server.quit()
            
            print(f"✅ hMailServer ({self.hmailserver_ip}) accessible")
            self.resultats_tests['hmailserver_connectivite'] = True
            return True
            
        except Exception as e:
            print(f"❌ Erreur hMailServer : {e}")
            self.resultats_tests['hmailserver_connectivite'] = False
            return False
    
    def test_3_configuration_relais(self):
        """Test 3: Configuration du relais Postfix"""
        print("\n🧪 TEST 3: CONFIGURATION RELAIS")
        print("-" * 35)
        
        try:
            with open("/etc/postfix/main.cf", "r") as f:
                config = f.read()
            
            # Vérifier relayhost
            if f"relayhost = [{self.hmailserver_ip}]" in config:
                print("✅ Relayhost configuré correctement")
                self.resultats_tests['config_relais'] = True
                return True
            else:
                print("❌ Relayhost mal configuré")
                self.resultats_tests['config_relais'] = False
                return False
                
        except Exception as e:
            print(f"❌ Erreur lecture config : {e}")
            self.resultats_tests['config_relais'] = False
            return False
    
    def test_4_envoi_direct_postfix(self):
        """Test 4: Envoi direct via Postfix"""
        print("\n🧪 TEST 4: ENVOI DIRECT POSTFIX")
        print("-" * 35)
        
        try:
            # Message de test simple
            msg = MIMEText("Test direct Postfix -> hMailServer")
            msg['Subject'] = "🧪 Test Direct Postfix"
            msg['From'] = "<EMAIL>"
            msg['To'] = "<EMAIL>"
            
            # Envoi via Postfix local
            server = smtplib.SMTP('127.0.0.1', 25)
            server.send_message(msg)
            server.quit()
            
            print("✅ Email envoyé via Postfix")
            self.resultats_tests['envoi_direct'] = True
            
            # Attendre un peu pour le relais
            print("⏳ Attente relais (5s)...")
            time.sleep(5)
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur envoi direct : {e}")
            self.resultats_tests['envoi_direct'] = False
            return False
    
    def test_5_app_phishing_lab(self):
        """Test 5: Application Phishing Lab"""
        print("\n🧪 TEST 5: APPLICATION PHISHING LAB")
        print("-" * 40)
        
        try:
            # Test API disponible
            response = requests.get(f'{self.app_url}/api/smtp-configs', timeout=5)
            
            if response.status_code == 200:
                print("✅ API Phishing Lab accessible")
                
                # Vérifier config SMTP Postfix
                configs = response.json()
                postfix_config = None
                
                for config in configs:
                    if config['server'] == '127.0.0.1' and config['port'] == 25:
                        postfix_config = config
                        break
                
                if postfix_config:
                    print("✅ Configuration SMTP Postfix trouvée")
                    if postfix_config['is_active']:
                        print("✅ Configuration Postfix active")
                        self.resultats_tests['app_config'] = True
                        return True
                    else:
                        print("⚠️ Configuration Postfix inactive")
                        self.resultats_tests['app_config'] = False
                        return False
                else:
                    print("❌ Configuration SMTP Postfix manquante")
                    self.resultats_tests['app_config'] = False
                    return False
            else:
                print(f"❌ API non accessible : {response.status_code}")
                self.resultats_tests['app_config'] = False
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Application Phishing Lab non démarrée")
            print("🔧 Démarrez avec : python app.py")
            self.resultats_tests['app_config'] = False
            return False
        except Exception as e:
            print(f"❌ Erreur app : {e}")
            self.resultats_tests['app_config'] = False
            return False
    
    def test_6_campagne_complete(self):
        """Test 6: Campagne complète avec tracking"""
        print("\n🧪 TEST 6: CAMPAGNE COMPLÈTE")
        print("-" * 35)
        
        email_test = input("📧 Email de test pour campagne complète : ").strip()
        if not email_test:
            print("⚠️ Test campagne ignoré (pas d'email)")
            self.resultats_tests['campagne_complete'] = None
            return True
        
        try:
            # Ajouter la cible
            cible_data = {
                'email': email_test,
                'first_name': 'Test',
                'last_name': 'Complet',
                'domain': email_test.split('@')[1]
            }
            
            requests.post(f'{self.app_url}/api/targets', json=cible_data)
            
            # Template de test
            template_test = """
            <html>
            <body style="font-family: Arial, sans-serif;">
                <h2>🧪 Test Chaîne Complète Phishing Lab</h2>
                <p>Bonjour {{TARGET_NAME}},</p>
                
                <p>Ce message teste la chaîne complète :</p>
                <ol>
                    <li>📤 Envoi depuis Linux (Postfix)</li>
                    <li>🔄 Relais via Windows (hMailServer)</li>
                    <li>📬 Réception dans Outlook</li>
                    <li>📊 Tracking complet</li>
                </ol>
                
                <div style="text-align: center; margin: 20px;">
                    <a href="{{CLICK_URL}}" 
                       style="background: #007bff; color: white; padding: 10px 20px; 
                              text-decoration: none; border-radius: 5px;">
                        🔗 Cliquez pour tester le tracking
                    </a>
                </div>
                
                <p><strong>Tests inclus :</strong></p>
                <ul>
                    <li>✅ Tracking d'ouverture (pixel invisible)</li>
                    <li>🖱️ Tracking de clic</li>
                    <li>📎 Pièces jointes avec tracking</li>
                </ul>
                
                <p>Si vous recevez ce message, la configuration fonctionne parfaitement !</p>
                
                <p><em>Phishing Lab - Test Automatisé</em></p>
                
                <!-- Pixel de tracking -->
                <img src="{{PIXEL_URL}}" width="1" height="1" style="display:none;">
            </body>
            </html>
            """
            
            # Données de la campagne
            email_data = {
                "target_email": email_test,
                "subject": "🧪 Test Chaîne Complète - Postfix/hMailServer/Outlook",
                "template": template_test,
                "attachments": ["Document_Securite.html"]
            }
            
            # Envoyer la campagne
            response = requests.post(f'{self.app_url}/api/send-email', json=email_data)
            
            if response.status_code == 200:
                data = response.json()
                token = data.get('token')
                
                print("✅ Campagne complète envoyée")
                print(f"🔑 Token de tracking : {token}")
                print(f"📊 Analyse : python analyser_token_complet.py {token}")
                
                self.resultats_tests['campagne_complete'] = True
                return token
            else:
                print(f"❌ Erreur envoi campagne : {response.status_code}")
                self.resultats_tests['campagne_complete'] = False
                return False
                
        except Exception as e:
            print(f"❌ Erreur campagne : {e}")
            self.resultats_tests['campagne_complete'] = False
            return False
    
    def afficher_resultats_finaux(self):
        """Affiche le résumé des tests"""
        print("\n" + "="*60)
        print("📊 RÉSULTATS TESTS CHAÎNE COMPLÈTE")
        print("="*60)
        
        tests = [
            ('postfix_local', 'Postfix Local'),
            ('hmailserver_connectivite', 'Connectivité hMailServer'),
            ('config_relais', 'Configuration Relais'),
            ('envoi_direct', 'Envoi Direct'),
            ('app_config', 'App Phishing Lab'),
            ('campagne_complete', 'Campagne Complète')
        ]
        
        succes = 0
        total = 0
        
        for key, nom in tests:
            if key in self.resultats_tests:
                resultat = self.resultats_tests[key]
                if resultat is True:
                    print(f"✅ {nom}")
                    succes += 1
                elif resultat is False:
                    print(f"❌ {nom}")
                elif resultat is None:
                    print(f"⚠️ {nom} (ignoré)")
                    continue
                total += 1
        
        print(f"\n📈 SCORE : {succes}/{total} tests réussis")
        
        if succes == total:
            print("\n🎉 CONFIGURATION PARFAITE !")
            print("✅ La chaîne Postfix -> hMailServer -> Outlook fonctionne")
        elif succes >= total * 0.8:
            print("\n✅ CONFIGURATION FONCTIONNELLE")
            print("⚠️ Quelques ajustements mineurs possibles")
        else:
            print("\n❌ CONFIGURATION À CORRIGER")
            print("🔧 Vérifiez les tests échoués")
        
        print(f"\n🔧 COMMANDES UTILES :")
        print(f"   - Logs Postfix : tail -f /var/log/postfix.log")
        print(f"   - Queue Postfix : postqueue -p")
        print(f"   - Test manuel : telnet {self.hmailserver_ip} 25")

def main():
    """Fonction principale"""
    print("🚀 TEST CHAÎNE COMPLÈTE PHISHING LAB")
    print("="*50)
    print("🎯 Linux (Postfix) -> Windows (hMailServer) -> Outlook")
    print()
    
    testeur = TesteurChaineComplete()
    
    # Détection hMailServer
    if not testeur.detecter_hmailserver():
        print("❌ Impossible de continuer sans hMailServer")
        return
    
    # Exécution des tests
    print("\n🧪 EXÉCUTION DES TESTS...")
    
    testeur.test_1_postfix_local()
    testeur.test_2_connectivite_hmailserver()
    testeur.test_3_configuration_relais()
    testeur.test_4_envoi_direct_postfix()
    testeur.test_5_app_phishing_lab()
    
    # Test campagne (optionnel)
    print("\n🎯 Test campagne complète (optionnel)")
    token = testeur.test_6_campagne_complete()
    
    # Résultats finaux
    testeur.afficher_resultats_finaux()
    
    if token:
        print(f"\n📧 VÉRIFIEZ VOTRE BOÎTE EMAIL")
        print(f"📊 Puis analysez : python analyser_token_complet.py {token}")

if __name__ == "__main__":
    main()
