#!/usr/bin/env python3
"""
Configuration Postfix/Dovecot pour Phishing Lab
Relais vers hMailServer sur VM Windows
"""

import subprocess
import os
import sys
import requests
import socket
from pathlib import Path

class PostfixDovecotConfigurator:
    """Configurateur pour Postfix/Dovecot avec relais hMailServer"""
    
    def __init__(self):
        self.hmailserver_ip = None
        self.hmailserver_port = 25
        self.local_domain = "phishing.lab"
        self.postfix_config_dir = "/etc/postfix"
        self.dovecot_config_dir = "/etc/dovecot"
        self.maildir_base = "/var/mail/vhosts"
        
    def detecter_hmailserver(self):
        """Détecte automatiquement l'IP de hMailServer"""
        print("🔍 DÉTECTION HMAILSERVER")
        print("="*40)
        
        # IPs communes pour VMs Windows
        ips_candidates = [
            "*************",  # IP commune VM
            "************",   # VirtualBox Host-Only
            "********",       # VirtualBox NAT
            "**********",     # VMware
        ]
        
        print("🔍 Test de connectivité hMailServer...")
        for ip in ips_candidates:
            if self.tester_hmailserver(ip):
                self.hmailserver_ip = ip
                print(f"✅ hMailServer trouvé : {ip}:25")
                return True
        
        # Demander manuellement
        print("❌ hMailServer non détecté automatiquement")
        ip_manuelle = input("📧 IP de votre VM Windows avec hMailServer : ").strip()
        
        if ip_manuelle and self.tester_hmailserver(ip_manuelle):
            self.hmailserver_ip = ip_manuelle
            print(f"✅ hMailServer configuré : {ip_manuelle}:25")
            return True
        
        print("❌ Impossible de se connecter à hMailServer")
        return False
    
    def tester_hmailserver(self, ip, port=25, timeout=3):
        """Teste la connectivité vers hMailServer"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def verifier_privileges(self):
        """Vérifie les privilèges root"""
        if os.geteuid() != 0:
            print("❌ Ce script nécessite les privilèges root")
            print("🔧 Relancez avec : sudo python3 configurer_postfix_dovecot.py")
            return False
        return True
    
    def installer_packages(self):
        """Installe Postfix et Dovecot"""
        print("\n📦 INSTALLATION POSTFIX/DOVECOT")
        print("="*40)
        
        packages = ["postfix", "dovecot-core", "dovecot-imapd", "dovecot-pop3d"]
        
        try:
            # Détecter le gestionnaire de paquets
            if self.command_exists("apt-get"):
                cmd = ["apt-get", "update"]
                subprocess.run(cmd, check=True)
                cmd = ["apt-get", "install", "-y"] + packages
            elif self.command_exists("yum"):
                cmd = ["yum", "install", "-y"] + packages
            elif self.command_exists("dnf"):
                cmd = ["dnf", "install", "-y"] + packages
            else:
                print("❌ Gestionnaire de paquets non supporté")
                return False
            
            print(f"📦 Installation : {' '.join(packages)}")
            subprocess.run(cmd, check=True)
            print("✅ Packages installés")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Erreur installation : {e}")
            return False
    
    def command_exists(self, command):
        """Vérifie si une commande existe"""
        return subprocess.run(["which", command], capture_output=True).returncode == 0
    
    def configurer_postfix(self):
        """Configure Postfix pour relais hMailServer"""
        print("\n🔧 CONFIGURATION POSTFIX")
        print("="*40)
        
        # Configuration Postfix principale
        postfix_main_cf = f"""# Configuration Postfix pour Phishing Lab
# Relais vers hMailServer

# Paramètres de base
myhostname = mail.{self.local_domain}
mydomain = {self.local_domain}
myorigin = $mydomain
inet_interfaces = all
inet_protocols = ipv4

# Domaines et destinations
mydestination = $myhostname, localhost.$mydomain, localhost, $mydomain
mynetworks = *********/8, ***********/16, 10.0.0.0/8, **********/12

# Relais vers hMailServer
relayhost = [{self.hmailserver_ip}]:{self.hmailserver_port}

# Sécurité et authentification
smtp_use_tls = no
smtp_sasl_auth_enable = no
smtp_sasl_security_options = 

# Maildir
home_mailbox = Maildir/
mailbox_command = 

# Taille des messages
message_size_limit = 50000000
mailbox_size_limit = 0

# Logs
mail_log_file = /var/log/postfix.log

# Désactiver la vérification DNS pour les tests
disable_dns_lookups = yes
smtp_host_lookup = native

# Configuration pour phishing lab
always_bcc = 
sender_bcc_maps = 
recipient_bcc_maps = 

# Accepter tous les domaines pour les tests
relay_domains = *
"""
        
        try:
            # Sauvegarder la config actuelle
            if os.path.exists(f"{self.postfix_config_dir}/main.cf"):
                subprocess.run([
                    "cp", 
                    f"{self.postfix_config_dir}/main.cf",
                    f"{self.postfix_config_dir}/main.cf.backup"
                ])
            
            # Écrire la nouvelle config
            with open(f"{self.postfix_config_dir}/main.cf", "w") as f:
                f.write(postfix_main_cf)
            
            print("✅ Configuration Postfix écrite")
            
            # Redémarrer Postfix
            subprocess.run(["systemctl", "restart", "postfix"], check=True)
            subprocess.run(["systemctl", "enable", "postfix"], check=True)
            
            print("✅ Postfix redémarré et activé")
            return True
            
        except Exception as e:
            print(f"❌ Erreur configuration Postfix : {e}")
            return False
    
    def configurer_dovecot(self):
        """Configure Dovecot pour Maildir"""
        print("\n🔧 CONFIGURATION DOVECOT")
        print("="*40)
        
        # Configuration Dovecot principale
        dovecot_conf = f"""# Configuration Dovecot pour Phishing Lab

# Protocoles
protocols = imap pop3

# Écoute
listen = *

# Maildir
mail_location = maildir:{self.maildir_base}/%d/%n/Maildir

# Authentification simple pour tests
disable_plaintext_auth = no
auth_mechanisms = plain login

# Base de données utilisateurs simple
passdb {{
    driver = passwd-file
    args = /etc/dovecot/users
}}

userdb {{
    driver = passwd-file
    args = /etc/dovecot/users
}}

# SSL désactivé pour tests locaux
ssl = no

# Logs
log_path = /var/log/dovecot.log
info_log_path = /var/log/dovecot-info.log
debug_log_path = /var/log/dovecot-debug.log

# Namespace
namespace inbox {{
    inbox = yes
    location = 
    mailbox Drafts {{
        special_use = \\Drafts
    }}
    mailbox Junk {{
        special_use = \\Junk
    }}
    mailbox Sent {{
        special_use = \\Sent
    }}
    mailbox "Sent Messages" {{
        special_use = \\Sent
    }}
    mailbox Trash {{
        special_use = \\Trash
    }}
}}

# Service IMAP
service imap-login {{
    inet_listener imap {{
        port = 143
    }}
}}

# Service POP3
service pop3-login {{
    inet_listener pop3 {{
        port = 110
    }}
}}
"""
        
        try:
            # Créer le répertoire de configuration
            os.makedirs(self.dovecot_config_dir, exist_ok=True)
            
            # Sauvegarder la config actuelle
            if os.path.exists(f"{self.dovecot_config_dir}/dovecot.conf"):
                subprocess.run([
                    "cp", 
                    f"{self.dovecot_config_dir}/dovecot.conf",
                    f"{self.dovecot_config_dir}/dovecot.conf.backup"
                ])
            
            # Écrire la nouvelle config
            with open(f"{self.dovecot_config_dir}/dovecot.conf", "w") as f:
                f.write(dovecot_conf)
            
            print("✅ Configuration Dovecot écrite")
            return True
            
        except Exception as e:
            print(f"❌ Erreur configuration Dovecot : {e}")
            return False

    def creer_utilisateurs_test(self):
        """Crée des utilisateurs de test pour Dovecot"""
        print("\n👤 CRÉATION UTILISATEURS TEST")
        print("="*40)

        # Utilisateurs de test
        users = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]

        try:
            # Créer le fichier des utilisateurs
            users_content = ""
            for user in users:
                # Mot de passe simple pour tests : "test123"
                users_content += f"{user}:{{PLAIN}}test123::::\n"

            with open("/etc/dovecot/users", "w") as f:
                f.write(users_content)

            # Créer les répertoires Maildir
            os.makedirs(self.maildir_base, exist_ok=True)

            for user in users:
                domain = user.split('@')[1]
                username = user.split('@')[0]
                maildir_path = f"{self.maildir_base}/{domain}/{username}/Maildir"

                os.makedirs(maildir_path, exist_ok=True)
                os.makedirs(f"{maildir_path}/cur", exist_ok=True)
                os.makedirs(f"{maildir_path}/new", exist_ok=True)
                os.makedirs(f"{maildir_path}/tmp", exist_ok=True)

                # Permissions
                subprocess.run(["chown", "-R", "mail:mail", f"{self.maildir_base}/{domain}"])
                subprocess.run(["chmod", "-R", "755", f"{self.maildir_base}/{domain}"])

            print("✅ Utilisateurs test créés")
            print("📧 Comptes disponibles :")
            for user in users:
                print(f"   - {user} (mot de passe: test123)")

            return True

        except Exception as e:
            print(f"❌ Erreur création utilisateurs : {e}")
            return False

    def configurer_app_phishing_lab(self):
        """Configure l'app Phishing Lab pour utiliser Postfix local"""
        print("\n🔧 CONFIGURATION APP PHISHING LAB")
        print("="*40)

        # Configuration SMTP pour Postfix local
        smtp_config = {
            'name': 'Postfix Local -> hMailServer',
            'server': '127.0.0.1',
            'port': 25,
            'username': '',  # Pas d'auth pour Postfix local
            'password': '',
            'use_tls': False,
            'use_ssl': False,
            'sender_name': 'Phishing Lab',
            'is_active': True
        }

        try:
            # Vérifier si l'app est démarrée
            response = requests.get('http://127.0.0.1:5000/api/smtp-configs', timeout=5)

            # Ajouter la configuration
            response = requests.post('http://127.0.0.1:5000/api/smtp-configs', json=smtp_config)

            if response.status_code in [201, 409]:
                print("✅ Configuration SMTP ajoutée à l'app")
                return True
            else:
                print(f"❌ Erreur configuration app : {response.status_code}")
                return False

        except requests.exceptions.ConnectionError:
            print("⚠️  App Phishing Lab non démarrée")
            print("🔧 Démarrez l'app avec : python app.py")
            print("📝 Configuration SMTP à ajouter manuellement :")
            print(f"   Serveur : {smtp_config['server']}")
            print(f"   Port : {smtp_config['port']}")
            print(f"   TLS : {smtp_config['use_tls']}")
            return False
        except Exception as e:
            print(f"❌ Erreur : {e}")
            return False

    def tester_configuration(self):
        """Teste la configuration complète"""
        print("\n🧪 TEST CONFIGURATION")
        print("="*40)

        # Test 1: Connectivité hMailServer
        print("1. Test connectivité hMailServer...")
        if self.tester_hmailserver(self.hmailserver_ip):
            print("   ✅ hMailServer accessible")
        else:
            print("   ❌ hMailServer inaccessible")
            return False

        # Test 2: Postfix
        print("2. Test Postfix...")
        try:
            result = subprocess.run(["systemctl", "is-active", "postfix"],
                                  capture_output=True, text=True)
            if result.stdout.strip() == "active":
                print("   ✅ Postfix actif")
            else:
                print("   ❌ Postfix inactif")
                return False
        except:
            print("   ❌ Erreur test Postfix")
            return False

        # Test 3: Dovecot
        print("3. Test Dovecot...")
        try:
            result = subprocess.run(["systemctl", "is-active", "dovecot"],
                                  capture_output=True, text=True)
            if result.stdout.strip() == "active":
                print("   ✅ Dovecot actif")
            else:
                print("   ❌ Dovecot inactif")
                return False
        except:
            print("   ❌ Erreur test Dovecot")
            return False

        # Test 4: Envoi test
        print("4. Test envoi email...")
        if self.envoyer_email_test():
            print("   ✅ Email test envoyé")
        else:
            print("   ❌ Erreur envoi test")
            return False

        print("\n🎉 CONFIGURATION RÉUSSIE !")
        print("="*40)
        print(f"📧 Postfix relais vers : {self.hmailserver_ip}:25")
        print(f"📬 Dovecot Maildir : {self.maildir_base}")
        print(f"🌐 Domaine local : {self.local_domain}")

        return True

    def envoyer_email_test(self):
        """Envoie un email de test via Postfix"""
        try:
            import smtplib
            from email.mime.text import MIMEText

            # Créer le message
            msg = MIMEText("Test Postfix -> hMailServer -> Outlook\n\nCe message teste la chaîne complète.")
            msg['Subject'] = "🧪 Test Phishing Lab - Postfix/hMailServer"
            msg['From'] = "<EMAIL>"
            msg['To'] = "<EMAIL>"

            # Envoyer via Postfix local
            server = smtplib.SMTP('127.0.0.1', 25)
            server.send_message(msg)
            server.quit()

            return True

        except Exception as e:
            print(f"   Erreur envoi : {e}")
            return False

    def demarrer_services(self):
        """Démarre Dovecot"""
        print("\n🚀 DÉMARRAGE SERVICES")
        print("="*40)

        try:
            # Dovecot
            subprocess.run(["systemctl", "restart", "dovecot"], check=True)
            subprocess.run(["systemctl", "enable", "dovecot"], check=True)
            print("✅ Dovecot démarré et activé")

            return True

        except Exception as e:
            print(f"❌ Erreur démarrage services : {e}")
            return False

def main():
    """Fonction principale"""
    print("🚀 CONFIGURATION POSTFIX/DOVECOT POUR PHISHING LAB")
    print("="*60)
    print("📧 Architecture : Linux (Postfix) -> Windows (hMailServer) -> Outlook")
    print()

    configurator = PostfixDovecotConfigurator()

    # Vérifications préliminaires
    if not configurator.verifier_privileges():
        return

    # Détecter hMailServer
    if not configurator.detecter_hmailserver():
        print("❌ Configuration impossible sans hMailServer")
        return

    # Installation
    if not configurator.installer_packages():
        print("❌ Installation échouée")
        return

    # Configuration
    if not configurator.configurer_postfix():
        print("❌ Configuration Postfix échouée")
        return

    if not configurator.configurer_dovecot():
        print("❌ Configuration Dovecot échouée")
        return

    if not configurator.creer_utilisateurs_test():
        print("❌ Création utilisateurs échouée")
        return

    if not configurator.demarrer_services():
        print("❌ Démarrage services échoué")
        return

    # Configuration app
    configurator.configurer_app_phishing_lab()

    # Tests
    if configurator.tester_configuration():
        print("\n🎉 CONFIGURATION TERMINÉE AVEC SUCCÈS !")
        print("\n📋 PROCHAINES ÉTAPES :")
        print("1. 🚀 Démarrez l'app : python app.py")
        print("2. 📧 Testez l'envoi d'emails")
        print("3. 📬 Vérifiez la réception dans Outlook")
        print("\n🔧 COMMANDES UTILES :")
        print("   - Logs Postfix : tail -f /var/log/postfix.log")
        print("   - Logs Dovecot : tail -f /var/log/dovecot.log")
        print("   - Status : systemctl status postfix dovecot")
    else:
        print("❌ Configuration échouée")

if __name__ == "__main__":
    main()
