#!/usr/bin/env python3
"""
Envoi d'emails via Postfix local vers hMailServer
Adaptation du code existant pour l'architecture Linux -> Windows
"""

import requests
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import os

class PostfixEmailSender:
    """Gestionnaire d'envoi d'emails via Postfix local"""
    
    def __init__(self):
        self.smtp_server = '127.0.0.1'
        self.smtp_port = 25
        self.local_domain = 'phishing.lab'
        self.app_url = 'http://127.0.0.1:5000'
        
    def configurer_smtp_postfix(self):
        """Configure l'app pour utiliser Postfix local"""
        print("🔧 CONFIGURATION SMTP POSTFIX")
        print("="*40)
        
        # Configuration SMTP pour Postfix local
        smtp_config = {
            'name': 'Postfix Local -> hMailServer',
            'server': self.smtp_server,
            'port': self.smtp_port,
            'username': '',  # Pas d'authentification pour Postfix local
            'password': '',
            'use_tls': False,
            'use_ssl': False,
            'sender_name': 'Phishing Lab',
            'is_active': True
        }
        
        try:
            # Désactiver les autres configurations
            response = requests.get(f'{self.app_url}/api/smtp-configs')
            if response.status_code == 200:
                configs = response.json()
                for config in configs:
                    if config['is_active']:
                        requests.post(f"{self.app_url}/api/smtp-configs/{config['id']}/deactivate")
            
            # Ajouter la nouvelle configuration
            response = requests.post(f'{self.app_url}/api/smtp-configs', json=smtp_config)
            
            if response.status_code in [201, 409]:
                print("✅ Configuration Postfix ajoutée et activée")
                return True
            else:
                print(f"❌ Erreur configuration : {response.status_code}")
                print(response.text)
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ App Phishing Lab non accessible")
            print("🔧 Démarrez l'app avec : python app.py")
            return False
        except Exception as e:
            print(f"❌ Erreur : {e}")
            return False
    
    def envoyer_email_direct(self, destinataire, sujet, contenu_html, expediteur=None, pieces_jointes=None):
        """Envoie un email directement via Postfix"""
        print(f"📧 ENVOI EMAIL VIA POSTFIX")
        print(f"   De : {expediteur or f'noreply@{self.local_domain}'}")
        print(f"   Vers : {destinataire}")
        print(f"   Sujet : {sujet}")
        
        try:
            # Créer le message
            msg = MIMEMultipart()
            msg['From'] = expediteur or f'noreply@{self.local_domain}'
            msg['To'] = destinataire
            msg['Subject'] = sujet
            
            # Ajouter le contenu HTML
            msg.attach(MIMEText(contenu_html, 'html'))
            
            # Ajouter les pièces jointes si présentes
            if pieces_jointes:
                for fichier in pieces_jointes:
                    if os.path.exists(f'attachments/{fichier}'):
                        self.ajouter_piece_jointe(msg, f'attachments/{fichier}')
                        print(f"📎 Pièce jointe ajoutée : {fichier}")
            
            # Envoyer via Postfix local
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            
            # Pas d'authentification nécessaire pour Postfix local
            text = msg.as_string()
            server.sendmail(msg['From'], [destinataire], text)
            server.quit()
            
            print("✅ Email envoyé via Postfix")
            return True
            
        except Exception as e:
            print(f"❌ Erreur envoi : {e}")
            return False
    
    def ajouter_piece_jointe(self, msg, chemin_fichier):
        """Ajoute une pièce jointe au message"""
        try:
            with open(chemin_fichier, 'rb') as f:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(f.read())
                
            encoders.encode_base64(part)
            
            filename = os.path.basename(chemin_fichier)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            
            msg.attach(part)
            
        except Exception as e:
            print(f"⚠️ Erreur pièce jointe {chemin_fichier}: {e}")
    
    def envoyer_campagne_phishing(self, email_destinataire, template_personnalise=None):
        """Envoie une campagne de phishing via l'API existante mais avec Postfix"""
        print(f"🎯 CAMPAGNE PHISHING VIA POSTFIX")
        print(f"   Destinataire : {email_destinataire}")
        
        # S'assurer que Postfix est configuré
        if not self.configurer_smtp_postfix():
            return False
        
        # Ajouter la cible si elle n'existe pas
        cible_data = {
            'email': email_destinataire,
            'first_name': email_destinataire.split('@')[0],
            'last_name': 'Test',
            'domain': email_destinataire.split('@')[1]
        }
        
        try:
            response = requests.post(f'{self.app_url}/api/targets', json=cible_data)
            print(f"📋 Cible ajoutée : {email_destinataire}")
        except:
            print(f"⚠️ Cible peut-être déjà existante")
        
        # Template par défaut optimisé pour hMailServer/Outlook
        template_defaut = template_personnalise or """
        <html>
        <head>
            <meta charset="utf-8">
            <title>Mise à jour de sécurité requise</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: #f8f9fa; padding: 20px; border-left: 4px solid #007bff;">
                    <h2 style="color: #007bff; margin-top: 0;">🔒 Mise à jour de sécurité requise</h2>
                    <p>Bonjour {{TARGET_NAME}},</p>
                    
                    <p>Votre compte nécessite une mise à jour de sécurité importante.</p>
                    
                    <div style="background: white; padding: 15px; margin: 20px 0; border: 1px solid #ddd;">
                        <h3>📎 Documents joints :</h3>
                        <p><strong>Procédures de sécurité mises à jour</strong></p>
                        <p>Veuillez télécharger et consulter les documents ci-joints.</p>
                    </div>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{{CLICK_URL}}" 
                           style="background: #007bff; color: white; padding: 12px 30px; 
                                  text-decoration: none; border-radius: 5px; display: inline-block;">
                            🔐 Mettre à jour maintenant
                        </a>
                    </div>
                    
                    <p><strong>Cette mise à jour inclut :</strong></p>
                    <ul>
                        <li>✅ Nouvelles procédures de sécurité</li>
                        <li>📋 Formulaires de conformité</li>
                        <li>🔑 Mise à jour des accès</li>
                    </ul>
                    
                    <p style="color: #666; font-size: 0.9em; margin-top: 30px;">
                        Cette mise à jour est obligatoire et doit être effectuée avant le 
                        <strong>{{DATE_LIMITE}}</strong>.
                    </p>
                    
                    <p>Cordialement,<br>
                    <strong>Service Informatique</strong></p>
                </div>
                
                <!-- Pixel de tracking invisible -->
                <img src="{{PIXEL_URL}}" width="1" height="1" style="display:none;">
            </div>
        </body>
        </html>
        """
        
        # Données de l'email
        email_data = {
            "target_email": email_destinataire,
            "subject": "🔒 Mise à jour de sécurité requise - Action immédiate",
            "template": template_defaut,
            "attachments": ["Procedures_Securite.docx", "Document_Securite.html"]
        }
        
        try:
            response = requests.post(f'{self.app_url}/api/send-email', json=email_data)
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Campagne envoyée via Postfix !")
                print(f"🔑 Token de tracking : {data.get('token', 'N/A')}")
                print(f"📊 Analyse : python analyser_token_complet.py {data.get('token', '')}")
                return data.get('token')
            else:
                print(f"❌ Erreur envoi campagne : {response.status_code}")
                print(response.text)
                return None
                
        except Exception as e:
            print(f"❌ Erreur : {e}")
            return None
    
    def tester_envoi_simple(self, destinataire):
        """Test d'envoi simple pour vérifier la connectivité"""
        print(f"🧪 TEST ENVOI SIMPLE")
        
        contenu_test = """
        <html>
        <body>
            <h2>🧪 Test Postfix -> hMailServer -> Outlook</h2>
            <p>Ce message teste la chaîne complète :</p>
            <ol>
                <li>📤 Envoi depuis Linux (Postfix)</li>
                <li>🔄 Relais via Windows (hMailServer)</li>
                <li>📬 Réception dans Outlook</li>
            </ol>
            <p>Si vous recevez ce message, la configuration fonctionne !</p>
            <p><em>Phishing Lab - Test de connectivité</em></p>
        </body>
        </html>
        """
        
        return self.envoyer_email_direct(
            destinataire=destinataire,
            sujet="🧪 Test Postfix/hMailServer - Connectivité",
            contenu_html=contenu_test,
            expediteur=f"test@{self.local_domain}"
        )

def main():
    """Fonction principale"""
    print("🚀 ENVOI EMAILS VIA POSTFIX/HMAILSERVER")
    print("="*50)
    print("📧 Architecture : Linux (Postfix) -> Windows (hMailServer) -> Outlook")
    print()
    
    sender = PostfixEmailSender()
    
    print("📋 CHOISISSEZ UNE OPTION :")
    print("1. 🧪 Test envoi simple")
    print("2. 🎯 Campagne phishing complète")
    print("3. 🔧 Configurer SMTP Postfix seulement")
    print("4. 📧 Envoi direct personnalisé")
    
    choix = input("\n🔢 Votre choix (1-4) : ").strip()
    
    if choix == '1':
        email = input("📧 Email de test : ").strip()
        if email:
            if sender.tester_envoi_simple(email):
                print("✅ Test réussi ! Vérifiez votre boîte email.")
            else:
                print("❌ Test échoué")
    
    elif choix == '2':
        email = input("📧 Email cible : ").strip()
        if email:
            token = sender.envoyer_campagne_phishing(email)
            if token:
                print(f"\n🎉 Campagne lancée !")
                print(f"📊 Suivi : python analyser_token_complet.py {token}")
    
    elif choix == '3':
        if sender.configurer_smtp_postfix():
            print("✅ Configuration SMTP Postfix terminée")
        else:
            print("❌ Configuration échouée")
    
    elif choix == '4':
        email = input("📧 Destinataire : ").strip()
        sujet = input("📝 Sujet : ").strip()
        if email and sujet:
            contenu = "<html><body><h2>Message personnalisé</h2><p>Contenu de test</p></body></html>"
            if sender.envoyer_email_direct(email, sujet, contenu):
                print("✅ Email envoyé")
    
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()
