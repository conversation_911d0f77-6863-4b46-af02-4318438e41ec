#!/usr/bin/env python3
"""
Script de démarrage rapide pour Postfix/hMailServer
Guide complet pour configurer et tester l'architecture
"""

import os
import subprocess
import sys

def afficher_banner():
    """Affiche le banner du script"""
    print("🚀 PHISHING LAB - POSTFIX/HMAILSERVER")
    print("="*50)
    print("📧 Architecture : Linux (Postfix) -> Windows (hMailServer) -> Outlook")
    print("🎯 Configuration automatisée pour campagnes de phishing")
    print()

def verifier_privileges():
    """Vérifie les privilèges root"""
    if os.geteuid() != 0:
        print("❌ Ce script nécessite les privilèges root pour configurer Postfix")
        print("🔧 Relancez avec : sudo python3 demarrer_postfix_hmailserver.py")
        return False
    return True

def verifier_fichiers():
    """Vérifie que tous les fichiers nécessaires sont présents"""
    fichiers_requis = [
        "configurer_postfix_dovecot.py",
        "configurer_relais_hmailserver.py", 
        "envoi_postfix_hmailserver.py",
        "tester_chaine_complete.py",
        "app.py"
    ]
    
    print("🔍 VÉRIFICATION FICHIERS")
    print("-" * 30)
    
    manquants = []
    for fichier in fichiers_requis:
        if os.path.exists(fichier):
            print(f"✅ {fichier}")
        else:
            print(f"❌ {fichier}")
            manquants.append(fichier)
    
    if manquants:
        print(f"\n❌ Fichiers manquants : {', '.join(manquants)}")
        return False
    
    print("✅ Tous les fichiers sont présents")
    return True

def menu_principal():
    """Affiche le menu principal"""
    print("\n📋 MENU PRINCIPAL")
    print("="*30)
    print("1. 🔧 Configuration complète (Postfix + Dovecot)")
    print("2. ⚡ Configuration relais uniquement")
    print("3. 🧪 Test de la chaîne complète")
    print("4. 📧 Envoi d'emails via Postfix")
    print("5. 🚀 Démarrer l'application Phishing Lab")
    print("6. 📊 Guide de dépannage")
    print("7. ❌ Quitter")
    print()

def configuration_complete():
    """Lance la configuration complète"""
    print("\n🔧 CONFIGURATION COMPLÈTE POSTFIX/DOVECOT")
    print("="*50)
    print("Cette option va :")
    print("- Installer Postfix et Dovecot")
    print("- Configurer le relais vers hMailServer")
    print("- Créer les utilisateurs de test")
    print("- Configurer l'app Phishing Lab")
    print()
    
    confirmer = input("Continuer ? (o/N) : ").strip().lower()
    if confirmer != 'o':
        return
    
    try:
        print("🚀 Lancement de la configuration complète...")
        subprocess.run([sys.executable, "configurer_postfix_dovecot.py"], check=True)
        print("✅ Configuration complète terminée")
    except subprocess.CalledProcessError:
        print("❌ Erreur lors de la configuration complète")
    except KeyboardInterrupt:
        print("\n⚠️ Configuration interrompue")

def configuration_relais():
    """Lance la configuration du relais uniquement"""
    print("\n⚡ CONFIGURATION RELAIS POSTFIX -> HMAILSERVER")
    print("="*50)
    print("Cette option va :")
    print("- Détecter hMailServer automatiquement")
    print("- Configurer Postfix pour le relais")
    print("- Tester la connectivité")
    print()
    
    try:
        print("🚀 Lancement de la configuration relais...")
        subprocess.run([sys.executable, "configurer_relais_hmailserver.py"], check=True)
        print("✅ Configuration relais terminée")
    except subprocess.CalledProcessError:
        print("❌ Erreur lors de la configuration relais")
    except KeyboardInterrupt:
        print("\n⚠️ Configuration interrompue")

def test_chaine():
    """Lance le test de la chaîne complète"""
    print("\n🧪 TEST CHAÎNE COMPLÈTE")
    print("="*30)
    print("Ce test va vérifier :")
    print("- Postfix local")
    print("- Connectivité hMailServer")
    print("- Configuration du relais")
    print("- Envoi d'emails de test")
    print("- Application Phishing Lab")
    print()
    
    try:
        print("🚀 Lancement des tests...")
        subprocess.run([sys.executable, "tester_chaine_complete.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ Erreur lors des tests")
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrompus")

def envoi_emails():
    """Lance l'interface d'envoi d'emails"""
    print("\n📧 ENVOI EMAILS VIA POSTFIX")
    print("="*35)
    
    try:
        subprocess.run([sys.executable, "envoi_postfix_hmailserver.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ Erreur lors de l'envoi")
    except KeyboardInterrupt:
        print("\n⚠️ Envoi interrompu")

def demarrer_app():
    """Démarre l'application Phishing Lab"""
    print("\n🚀 DÉMARRAGE APPLICATION PHISHING LAB")
    print("="*45)
    
    if not os.path.exists("app.py"):
        print("❌ Fichier app.py non trouvé")
        return
    
    print("🔧 Démarrage de l'application...")
    print("📡 L'app sera accessible sur : http://127.0.0.1:5000")
    print("⚠️ Appuyez sur Ctrl+C pour arrêter")
    print()
    
    try:
        subprocess.run([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n🛑 Application arrêtée")

def guide_depannage():
    """Affiche le guide de dépannage"""
    print("\n📊 GUIDE DE DÉPANNAGE")
    print("="*35)
    
    print("\n🔧 COMMANDES UTILES :")
    print("   - Status Postfix : systemctl status postfix")
    print("   - Logs Postfix : tail -f /var/log/postfix.log")
    print("   - Queue Postfix : postqueue -p")
    print("   - Flush queue : postqueue -f")
    print("   - Test config : postfix check")
    
    print("\n🔍 DIAGNOSTIC RÉSEAU :")
    print("   - Test port hMailServer : telnet IP_HMAILSERVER 25")
    print("   - Test DNS : nslookup phishing.lab")
    print("   - Test connectivité : ping IP_HMAILSERVER")
    
    print("\n❌ PROBLÈMES COURANTS :")
    print("   1. hMailServer inaccessible :")
    print("      → Vérifiez l'IP de la VM Windows")
    print("      → Vérifiez le firewall Windows")
    print("      → Vérifiez que hMailServer écoute sur port 25")
    
    print("\n   2. Postfix ne démarre pas :")
    print("      → Vérifiez la configuration : postfix check")
    print("      → Vérifiez les logs : journalctl -u postfix")
    
    print("\n   3. Emails non reçus :")
    print("      → Vérifiez la queue : postqueue -p")
    print("      → Vérifiez les logs Postfix")
    print("      → Vérifiez la configuration Outlook")
    
    print("\n📧 FICHIERS DE CONFIGURATION :")
    print("   - Postfix : /etc/postfix/main.cf")
    print("   - Dovecot : /etc/dovecot/dovecot.conf")
    print("   - Logs : /var/log/postfix.log")
    
    print("\n🔄 REDÉMARRAGE SERVICES :")
    print("   - Postfix : systemctl restart postfix")
    print("   - Dovecot : systemctl restart dovecot")
    
    input("\nAppuyez sur Entrée pour continuer...")

def main():
    """Fonction principale"""
    afficher_banner()
    
    # Vérifications préliminaires
    if not verifier_fichiers():
        print("❌ Vérifiez que tous les fichiers sont présents")
        return
    
    while True:
        menu_principal()
        
        try:
            choix = input("🔢 Votre choix (1-7) : ").strip()
            
            if choix == '1':
                if not verifier_privileges():
                    continue
                configuration_complete()
            
            elif choix == '2':
                if not verifier_privileges():
                    continue
                configuration_relais()
            
            elif choix == '3':
                test_chaine()
            
            elif choix == '4':
                envoi_emails()
            
            elif choix == '5':
                demarrer_app()
            
            elif choix == '6':
                guide_depannage()
            
            elif choix == '7':
                print("👋 Au revoir !")
                break
            
            else:
                print("❌ Choix invalide")
        
        except KeyboardInterrupt:
            print("\n👋 Au revoir !")
            break
        except Exception as e:
            print(f"❌ Erreur : {e}")

if __name__ == "__main__":
    main()
