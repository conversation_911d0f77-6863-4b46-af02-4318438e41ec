# Configuration Postfix/hMailServer pour Phishing Lab

## 🎯 Architecture

```
Linux VM (Postfix) → Windows VM (hMailServer) → Outlook
```

Cette configuration permet d'envoyer des emails de phishing depuis votre VM Linux via Postfix, qui les relaie vers hMailServer sur votre VM Windows, puis vers Outlook.

## 📋 Prérequis

### VM Linux
- Ubuntu/Debian ou CentOS/RHEL
- Privilèges root
- Python 3.6+
- Accès réseau vers la VM Windows

### VM Windows  
- hMailServer installé et configuré
- Port 25 ouvert (SMTP)
- Compte email configuré dans Outlook

## 🚀 Installation Rapide

### 1. Démarrage automatique
```bash
sudo python3 demarrer_postfix_hmailserver.py
```

### 2. Configuration complète (recommandé)
```bash
sudo python3 configurer_postfix_dovecot.py
```

### 3. Configuration relais uniquement
```bash
sudo python3 configurer_relais_hmailserver.py
```

## 📧 Utilisation

### Test de la chaîne complète
```bash
python3 tester_chaine_complete.py
```

### Envoi d'emails
```bash
python3 envoi_postfix_hmailserver.py
```

### Démarrage de l'application
```bash
python3 app.py
```

## 🔧 Configuration Manuelle

### Postfix (si nécessaire)

1. **Installer Postfix**
```bash
sudo apt-get update
sudo apt-get install postfix
```

2. **Configurer le relais** dans `/etc/postfix/main.cf`
```
relayhost = [IP_HMAILSERVER]:25
mydomain = phishing.lab
myorigin = $mydomain
```

3. **Redémarrer Postfix**
```bash
sudo systemctl restart postfix
sudo systemctl enable postfix
```

### hMailServer

1. **Vérifier la configuration**
   - Port SMTP : 25
   - Accepter les connexions externes
   - Domaine configuré

2. **Tester la connectivité**
```bash
telnet IP_HMAILSERVER 25
```

## 🧪 Tests

### Test connectivité hMailServer
```bash
telnet ************* 25
```

### Test envoi Postfix
```bash
echo "Test" | mail -s "Test Postfix" <EMAIL>
```

### Vérifier les logs
```bash
tail -f /var/log/postfix.log
```

### Vérifier la queue
```bash
postqueue -p
```

## 📊 Monitoring

### Logs Postfix
```bash
tail -f /var/log/postfix.log
```

### Status des services
```bash
systemctl status postfix
systemctl status dovecot
```

### Queue Postfix
```bash
postqueue -p    # Voir la queue
postqueue -f    # Forcer l'envoi
```

## ❌ Dépannage

### Problèmes courants

1. **hMailServer inaccessible**
   - Vérifiez l'IP de la VM Windows
   - Vérifiez le firewall Windows
   - Vérifiez que hMailServer écoute sur le port 25

2. **Postfix ne démarre pas**
   - Vérifiez la configuration : `postfix check`
   - Vérifiez les logs : `journalctl -u postfix`

3. **Emails non reçus**
   - Vérifiez la queue : `postqueue -p`
   - Vérifiez les logs Postfix
   - Vérifiez la configuration Outlook

### Commandes de diagnostic

```bash
# Test configuration Postfix
postfix check

# Logs détaillés
journalctl -u postfix -f

# Test manuel SMTP
telnet 127.0.0.1 25

# Flush queue
postqueue -f

# Redémarrer services
sudo systemctl restart postfix
sudo systemctl restart dovecot
```

## 📁 Structure des fichiers

```
phishing_lab/
├── configurer_postfix_dovecot.py      # Configuration complète
├── configurer_relais_hmailserver.py   # Configuration relais
├── envoi_postfix_hmailserver.py       # Interface d'envoi
├── tester_chaine_complete.py          # Tests automatisés
├── demarrer_postfix_hmailserver.py    # Script de démarrage
└── README_POSTFIX_HMAILSERVER.md      # Cette documentation
```

## 🎯 Utilisation pour campagnes

### 1. Configurer l'architecture
```bash
sudo python3 demarrer_postfix_hmailserver.py
# Choisir option 1 (Configuration complète)
```

### 2. Tester la chaîne
```bash
python3 tester_chaine_complete.py
```

### 3. Lancer une campagne
```bash
python3 envoi_postfix_hmailserver.py
# Choisir option 2 (Campagne phishing complète)
```

### 4. Analyser les résultats
```bash
python3 analyser_token_complet.py TOKEN
```

## 🔒 Sécurité

- Cette configuration est destinée aux tests et formations
- Utilisez uniquement dans un environnement contrôlé
- Ne pas utiliser pour de vrais emails malveillants
- Respectez les lois locales sur la cybersécurité

## 📞 Support

En cas de problème :

1. Vérifiez les logs : `tail -f /var/log/postfix.log`
2. Testez la connectivité : `telnet IP_HMAILSERVER 25`
3. Vérifiez la configuration : `postfix check`
4. Utilisez le guide de dépannage intégré

## 🔄 Mise à jour

Pour mettre à jour la configuration :

```bash
sudo python3 configurer_relais_hmailserver.py
```

## 📈 Optimisations

### Performance
- Ajustez `maximal_queue_lifetime` dans Postfix
- Configurez `smtp_connect_timeout` selon votre réseau

### Fiabilité  
- Surveillez les logs régulièrement
- Testez la connectivité périodiquement
- Sauvegardez les configurations

---

**Note** : Cette configuration est optimisée pour les laboratoires de test de phishing et les formations en cybersécurité.
