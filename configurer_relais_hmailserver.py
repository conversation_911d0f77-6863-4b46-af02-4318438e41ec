#!/usr/bin/env python3
"""
Configuration du relais Postfix vers hMailServer
Script spécialisé pour la configuration du relais
"""

import subprocess
import os
import socket
import time

class RelaisHMailServerConfigurator:
    """Configurateur spécialisé pour le relais vers hMailServer"""
    
    def __init__(self):
        self.hmailserver_ip = None
        self.hmailserver_port = 25
        self.postfix_config_dir = "/etc/postfix"
        
    def detecter_hmailserver_avance(self):
        """Détection avancée de hMailServer avec test SMTP"""
        print("🔍 DÉTECTION AVANCÉE HMAILSERVER")
        print("="*45)
        
        # IPs candidates étendues
        ips_candidates = [
            "*************", "*************", "*************",
            "************", "************00", "************01",
            "********", "*********", "**********",
            "**********", "**********", "**********00",
            "*************", "*************"
        ]
        
        print("🔍 Scan des IPs candidates...")
        serveurs_trouves = []
        
        for ip in ips_candidates:
            print(f"   Test {ip}:25...", end=" ")
            if self.tester_smtp_complet(ip):
                print("✅ SMTP OK")
                serveurs_trouves.append(ip)
            else:
                print("❌")
        
        if serveurs_trouves:
            print(f"\n✅ Serveurs SMTP trouvés : {serveurs_trouves}")
            
            if len(serveurs_trouves) == 1:
                self.hmailserver_ip = serveurs_trouves[0]
                print(f"🎯 hMailServer sélectionné : {self.hmailserver_ip}")
                return True
            else:
                print("🤔 Plusieurs serveurs trouvés, sélection manuelle :")
                for i, ip in enumerate(serveurs_trouves, 1):
                    print(f"   {i}. {ip}")
                
                try:
                    choix = int(input("Choisissez (numéro) : ")) - 1
                    if 0 <= choix < len(serveurs_trouves):
                        self.hmailserver_ip = serveurs_trouves[choix]
                        print(f"✅ hMailServer sélectionné : {self.hmailserver_ip}")
                        return True
                except:
                    pass
        
        # Saisie manuelle
        print("\n❌ Aucun serveur détecté automatiquement")
        ip_manuelle = input("📧 IP de votre hMailServer : ").strip()
        
        if ip_manuelle and self.tester_smtp_complet(ip_manuelle):
            self.hmailserver_ip = ip_manuelle
            print(f"✅ hMailServer configuré : {ip_manuelle}")
            return True
        
        print("❌ Impossible de se connecter à hMailServer")
        return False
    
    def tester_smtp_complet(self, ip, port=25, timeout=2):
        """Test SMTP complet avec dialogue"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            
            # Connexion
            sock.connect((ip, port))
            
            # Lire la bannière
            response = sock.recv(1024).decode('utf-8', errors='ignore')
            
            # Test EHLO
            sock.send(b'EHLO test.local\r\n')
            response = sock.recv(1024).decode('utf-8', errors='ignore')
            
            # Fermer proprement
            sock.send(b'QUIT\r\n')
            sock.recv(1024)
            sock.close()
            
            # Vérifier que c'est bien un serveur SMTP
            return '220' in response or '250' in response
            
        except:
            return False
    
    def configurer_relais_postfix(self):
        """Configure Postfix spécifiquement pour le relais hMailServer"""
        print("\n🔧 CONFIGURATION RELAIS POSTFIX")
        print("="*40)
        
        if not self.hmailserver_ip:
            print("❌ IP hMailServer non définie")
            return False
        
        # Configuration optimisée pour relais hMailServer
        config_relais = f"""# Configuration Postfix - Relais hMailServer
# Optimisée pour Windows hMailServer

# Paramètres de base
myhostname = mail.phishing.lab
mydomain = phishing.lab
myorigin = $mydomain
inet_interfaces = all
inet_protocols = ipv4

# Destinations locales (minimal pour relais)
mydestination = $myhostname, localhost.$mydomain, localhost
mynetworks = *********/8, ***********/16, 10.0.0.0/8, **********/12

# RELAIS PRINCIPAL vers hMailServer
relayhost = [{self.hmailserver_ip}]:{self.hmailserver_port}

# Tous les domaines sont relayés
relay_domains = *

# Pas d'authentification pour le relais local
smtp_sasl_auth_enable = no
smtp_use_tls = no
smtp_enforce_tls = no

# Optimisations pour hMailServer
smtp_connect_timeout = 30s
smtp_helo_timeout = 30s
smtp_mail_timeout = 30s
smtp_rcpt_timeout = 30s
smtp_data_timeout = 120s

# Désactiver vérifications DNS pour tests
disable_dns_lookups = yes
smtp_host_lookup = native

# Gestion des erreurs
smtp_skip_quit_response = yes
smtp_always_send_ehlo = yes

# Taille des messages
message_size_limit = 50000000
mailbox_size_limit = 0

# Logs détaillés
mail_log_file = /var/log/postfix.log
debug_peer_list = {self.hmailserver_ip}
debug_peer_level = 2

# Queue management
maximal_queue_lifetime = 1d
bounce_queue_lifetime = 1d
delay_warning_time = 4h

# Maildir local (optionnel)
home_mailbox = Maildir/

# Désactiver les vérifications locales
local_recipient_maps = 
unknown_local_recipient_reject_code = 550

# Configuration pour tests de phishing
always_bcc = 
sender_bcc_maps = 
recipient_bcc_maps = 

# Accepter tous les expéditeurs pour tests
smtpd_sender_restrictions = 
smtpd_recipient_restrictions = permit_mynetworks, permit_sasl_authenticated, reject_unauth_destination
"""
        
        try:
            # Sauvegarder la config actuelle
            if os.path.exists(f"{self.postfix_config_dir}/main.cf"):
                subprocess.run([
                    "cp", 
                    f"{self.postfix_config_dir}/main.cf",
                    f"{self.postfix_config_dir}/main.cf.backup.relais"
                ])
                print("💾 Configuration actuelle sauvegardée")
            
            # Écrire la nouvelle config
            with open(f"{self.postfix_config_dir}/main.cf", "w") as f:
                f.write(config_relais)
            
            print("✅ Configuration relais écrite")
            
            # Vérifier la configuration
            result = subprocess.run(["postfix", "check"], capture_output=True, text=True)
            if result.returncode != 0:
                print(f"⚠️ Avertissement config : {result.stderr}")
            else:
                print("✅ Configuration validée")
            
            # Redémarrer Postfix
            subprocess.run(["systemctl", "restart", "postfix"], check=True)
            subprocess.run(["systemctl", "enable", "postfix"], check=True)
            
            print("✅ Postfix redémarré avec config relais")
            return True
            
        except Exception as e:
            print(f"❌ Erreur configuration relais : {e}")
            return False
    
    def tester_relais(self):
        """Teste le relais Postfix -> hMailServer"""
        print("\n🧪 TEST RELAIS POSTFIX -> HMAILSERVER")
        print("="*45)
        
        try:
            import smtplib
            from email.mime.text import MIMEText
            
            # Message de test
            msg = MIMEText(f"""Test du relais Postfix vers hMailServer

Configuration testée :
- Postfix local : 127.0.0.1:25
- hMailServer : {self.hmailserver_ip}:25
- Timestamp : {time.strftime('%Y-%m-%d %H:%M:%S')}

Si vous recevez ce message, le relais fonctionne !
""")
            
            msg['Subject'] = "🧪 Test Relais Postfix -> hMailServer"
            msg['From'] = "<EMAIL>"
            msg['To'] = "<EMAIL>"
            
            print("📤 Envoi via Postfix local...")
            
            # Connexion à Postfix local
            server = smtplib.SMTP('127.0.0.1', 25)
            server.set_debuglevel(1)  # Debug pour voir les échanges
            
            # Envoyer
            server.send_message(msg)
            server.quit()
            
            print("✅ Message envoyé à Postfix")
            print("🔄 Postfix devrait relayer vers hMailServer")
            print(f"📧 Vérifiez la réception dans hMailServer/Outlook")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur test relais : {e}")
            return False
    
    def verifier_logs_postfix(self):
        """Vérifie les logs Postfix pour diagnostiquer"""
        print("\n📋 VÉRIFICATION LOGS POSTFIX")
        print("="*40)
        
        try:
            # Logs récents
            result = subprocess.run([
                "tail", "-20", "/var/log/postfix.log"
            ], capture_output=True, text=True)
            
            if result.stdout:
                print("📄 Logs récents :")
                print(result.stdout)
            else:
                print("⚠️ Pas de logs récents trouvés")
                
            # Vérifier les erreurs
            result = subprocess.run([
                "grep", "-i", "error\\|warning\\|reject", "/var/log/postfix.log"
            ], capture_output=True, text=True)
            
            if result.stdout:
                print("\n⚠️ Erreurs/Avertissements :")
                print(result.stdout[-1000:])  # Derniers 1000 caractères
            else:
                print("✅ Pas d'erreurs récentes")
                
        except Exception as e:
            print(f"❌ Erreur lecture logs : {e}")
    
    def afficher_status_complet(self):
        """Affiche le status complet de la configuration"""
        print("\n📊 STATUS CONFIGURATION RELAIS")
        print("="*45)
        
        # Status Postfix
        try:
            result = subprocess.run(["systemctl", "is-active", "postfix"], 
                                  capture_output=True, text=True)
            status_postfix = result.stdout.strip()
            print(f"📧 Postfix : {status_postfix}")
        except:
            print("📧 Postfix : Erreur")
        
        # Connectivité hMailServer
        if self.hmailserver_ip:
            if self.tester_smtp_complet(self.hmailserver_ip):
                print(f"🔗 hMailServer ({self.hmailserver_ip}) : ✅ Accessible")
            else:
                print(f"🔗 hMailServer ({self.hmailserver_ip}) : ❌ Inaccessible")
        
        # Configuration
        if os.path.exists(f"{self.postfix_config_dir}/main.cf"):
            with open(f"{self.postfix_config_dir}/main.cf", "r") as f:
                config = f.read()
                if "relayhost" in config and self.hmailserver_ip in config:
                    print("⚙️ Configuration relais : ✅ OK")
                else:
                    print("⚙️ Configuration relais : ❌ Manquante")
        
        print(f"\n🔧 COMMANDES UTILES :")
        print(f"   - Logs temps réel : tail -f /var/log/postfix.log")
        print(f"   - Queue Postfix : postqueue -p")
        print(f"   - Flush queue : postqueue -f")
        print(f"   - Test manuel : telnet {self.hmailserver_ip} 25")

def main():
    """Fonction principale"""
    print("🚀 CONFIGURATION RELAIS POSTFIX -> HMAILSERVER")
    print("="*55)
    print("🎯 Configuration spécialisée pour le relais email")
    print()
    
    if os.geteuid() != 0:
        print("❌ Ce script nécessite les privilèges root")
        print("🔧 Relancez avec : sudo python3 configurer_relais_hmailserver.py")
        return
    
    configurator = RelaisHMailServerConfigurator()
    
    # Détection hMailServer
    if not configurator.detecter_hmailserver_avance():
        print("❌ Configuration impossible sans hMailServer accessible")
        return
    
    # Configuration du relais
    if not configurator.configurer_relais_postfix():
        print("❌ Configuration du relais échouée")
        return
    
    # Test du relais
    print("\n🧪 Test du relais...")
    if configurator.tester_relais():
        print("✅ Test envoyé")
    
    # Vérification des logs
    configurator.verifier_logs_postfix()
    
    # Status final
    configurator.afficher_status_complet()
    
    print("\n🎉 CONFIGURATION RELAIS TERMINÉE !")
    print("\n📋 PROCHAINES ÉTAPES :")
    print("1. 🚀 Testez avec : python envoi_postfix_hmailserver.py")
    print("2. 📧 Vérifiez la réception dans Outlook")
    print("3. 📊 Surveillez les logs : tail -f /var/log/postfix.log")

if __name__ == "__main__":
    main()
